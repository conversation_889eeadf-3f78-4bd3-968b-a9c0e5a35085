import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { takeUntil } from 'rxjs';
import { Account } from 'src/app/auth/models/user.model';
import { AuthService } from 'src/app/auth/services';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse } from 'src/app/shared/models';
import { FirebaseSubscriptionModel } from 'src/app/shared/models/firebase-subscription.model';
import { StorageItem } from 'src/app/shared/services/local-storage.service';
import { MessagingService } from 'src/app/shared/services/messaging.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { SignalRService } from 'src/app/shared/services/signalr.service';
import { CommonUtils } from 'src/app/shared/utils';
import { AllowedPages } from './models';

@Component({
  selector: 'app-header-elements',
  templateUrl: './header-elements.component.html',
  styleUrls: ['./header-elements.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default
})
export class HeaderElementsComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() override currentUser!: Account | null;

  @ViewChild('logoutDialog') logoutDialog!: TemplateRef<any>;

  userInitials!: string;
  dialogRef!: MatDialogRef<any>;
  isNotificationPanelOpen = false;
  showDependentInfo = false;
  unreadNotificationCount = 0;
  selectedDependentId!: number;
  allowedPages!: AllowedPages[];

  constructor(
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef,
    private readonly dialog: MatDialog,
    private readonly signalRService: SignalRService,
    private readonly notificationService: NotificationService,
    private readonly messagingService: MessagingService,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['currentUser']?.currentValue) {
      this.currentUser = changes['currentUser']?.currentValue;
      this.setInitials(this.currentUser?.firstName, this.currentUser?.lastName);
    }
  }

  ngOnInit(): void {
    this.isDashboardRoute();
    this.setDependentIdFromQueryParams();
    this.getCurrentUser();
    this.getUnreadNotificationCount();
  }

  isDashboardRoute(): void {
    this.router.events.pipe(takeUntil(this.destroy$)).subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.showDependentInfo = this.isAllowedPage();
        this.cdr.detectChanges();
      }
    });
    this.showDependentInfo = this.isAllowedPage();
  }

  setDependentIdFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.dependentId) {
        this.selectedDependentId = +params.dependentId;
      } else {
        this.selectedDependentId = 0;
      }
      this.cdr.detectChanges();
    });
  }

  getCurrentUser(): void {
    this.authService
      .getCurrentUser$()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Account | null) => {
          this.currentUser = res;
          this.cdr.detectChanges();
        }
      });
  }

  setInitials(firstName?: string, lastName?: string): string {
    return !this.currentUser?.lastName ? CommonUtils.getInitialsUsingFullName(firstName) : CommonUtils.getInitials(firstName, lastName);
  }

  onSelectDependent(id: number): void {
    this.selectedDependentId = id;
    let queryParams = id ? { dependentId: id } : {};

    const urlTree = this.router.parseUrl(this.router.url);
    const currentPath = '/' + urlTree.root.children['primary']?.segments.map(segment => segment.path).join('/');
    const currentQueryParams = urlTree.queryParams;
  
    const matchedPage = this.allowedPages.find(page => {
      const pathMatch = currentPath === page.path;
      
      if (pathMatch && page.queryParams) {
        return Object.entries(page.queryParams).every(([key, value]) => 
          currentQueryParams[key] === value
        );
      }
      
      return pathMatch;
    });
    
    if (matchedPage) {
      if (matchedPage.queryParams) {
        queryParams = { ...matchedPage.queryParams, ...queryParams };
      }

      this.router.navigate([matchedPage.path], { queryParams });
    } 
  }

  isAllowedPage(): boolean {
    this.allowedPages = [
      { path: `${this.path.root}${this.path.dashboard.root}` },
      { path: `${this.path.root}${this.path.billing.root}/${this.path.billing.planAndPass}`, queryParams: { activeTab: 'Open Bills' } },
      { path: `${this.path.root}${this.path.billing.root}/${this.path.billing.planAndPass}`, queryParams: { activeTab: 'Bill History' } },
      { path: `${this.path.root}${this.path.billing.root}/${this.path.billing.product}` }
    ];

    const urlTree = this.router.parseUrl(this.router.url);
    const currentQueryParams = urlTree.queryParams;

    return this.allowedPages.some(page => {
      const pathMatch = this.router.url.startsWith(page.path);
      if (page.queryParams) {
        return pathMatch && Object.entries(page.queryParams).every(([key, value]) => currentQueryParams[key] === value);
      }
      return pathMatch;
    });
  }

  openLogoutConfirmationDialog(): void {
    this.dialogRef = this.dialog.open(this.logoutDialog, {
      width: '400px'
    });
  }

  closeDialog(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  toggleNotificationPanel(isOpen: boolean): void {
    this.isNotificationPanelOpen = isOpen;
  }

  getUnreadNotificationCount(): void {
    this.notificationService
      .getList<CBGetResponse<number>>(API_URL.notifications.getTotalCountForUnreadNotification)
      .pipe(takeUntil(this.destroy$))
      .subscribe((res: CBGetResponse<number>) => {
        this.unreadNotificationCount = res.result;
        this.cdr.detectChanges();
      });
  }

  confirmLogout(): void {
    this.closeDialog();
    this.logout();
  }

  get getFirebaseParams(): FirebaseSubscriptionModel {
    return {
      userId: this.currentUser?.userId,
      deviceId: localStorage.getItem(StorageItem.DeviceUUID)!
    };
  }

  logout(): void {
    this.signalRService.stopConnection();
    if (this.getFirebaseParams.deviceId && this.getFirebaseParams.userId) {
      this.messagingService.unRegisterToken(this.getFirebaseParams);
    }
    setTimeout(() => {
      this.authService.logOut();
    }, 0);
  }
}
